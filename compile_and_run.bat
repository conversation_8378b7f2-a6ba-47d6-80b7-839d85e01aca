@echo off
REM Compile and <PERSON> Script for Mini Ticket System (Windows)
REM Academic project for BIT 4043 OOP course

echo ==========================================
echo Mini Ticket System - Compilation ^& Run
echo BIT 4043 Object-Oriented Programming
echo ==========================================

REM Clean previous compilation
echo Cleaning previous compilation...
del /Q *.class 2>nul

REM Compile all Java files
echo Compiling Java files...
javac *.java

REM Check if compilation was successful
if %errorlevel% equ 0 (
    echo Compilation successful!
    echo Starting Mini Ticket System...
    echo ==========================================
    
    REM Run the application
    java Main
) else (
    echo Compilation failed! Please check for errors.
    pause
    exit /b 1
)

pause
