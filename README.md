# Mini Ticket System

**Academic Project for BIT 4043 Object-Oriented Programming Course**

## Project Overview

This is a simplified ticket booking system built using Java Swing that demonstrates core OOP concepts and basic GUI development. The application follows academic requirements with three main modules and uses in-memory data storage.

## Features

### 🏠 **Module 1: Home Screen**
- Display system information and branding
- Show available events with details (name, date, venue, price)
- Professional layout with images, text, and labels
- Navigation to booking form

### 📝 **Module 2: Booking Form**
- Customer information input (name, email, phone)
- Event selection and seat type options
- Quantity selection with spinner control
- **Real-time price calculation** based on seat type and quantity
- Form validation and error handling

### 🧾 **Module 3: Receipt Display**
- Complete booking summary
- Customer and event details
- Ticket information and total price
- Professional receipt format
- Navigation options

## Technical Specifications

### **Technology Stack**
- **Language**: Java SE (no external dependencies)
- **GUI Framework**: Java Swing
- **Architecture**: Simplified MVC pattern
- **Data Storage**: In-memory ArrayLists (no database)

### **Core Components Used**
- `J<PERSON>rame` - Main application window
- `JPanel` - Layout containers
- `<PERSON><PERSON><PERSON><PERSON>` - Text and information display
- `JTextField` - Text input fields
- `J<PERSON><PERSON>on` - Action buttons
- `JComboBox` - Dropdown selections
- `JSpinner` - Numeric input
- `JTextArea` - Multi-line text display
- `JScrollPane` - Scrollable content
- `CardLayout` - Panel switching

### **Project Structure**
```
MiniTicketSystem/
├── Main.java                          # Application entry point
├── model/
│   ├── Event.java                     # Event data model
│   └── Booking.java                   # Booking data model
├── view/
│   ├── MainFrame.java                 # Main application window
│   ├── HomePanel.java                 # Home screen display
│   ├── BookingFormPanel.java          # Ticket booking form
│   └── ReceiptPanel.java              # Receipt display
├── controller/
│   └── BookingController.java         # Business logic controller
├── compile_and_run.sh                 # Compilation script
└── README.md                          # This file
```

## How to Run

### **Method 1: Using the Script (Recommended)**
```bash
cd MiniTicketSystem
./compile_and_run.sh
```

### **Method 2: Manual Compilation**
```bash
cd MiniTicketSystem

# Compile all Java files
javac -d . *.java model/*.java view/*.java controller/*.java

# Run the application
java Main
```

## Sample Data

The application comes pre-loaded with 5 sample events:
1. **Rock Concert 2025** - City Arena ($50.00)
2. **Classical Music Evening** - Symphony Hall ($75.00)
3. **Comedy Show** - Comedy Club ($30.00)
4. **Jazz Festival** - Jazz Lounge ($60.00)
5. **Theater Play** - Grand Theater ($40.00)

## Seat Types & Pricing

- **Standard**: Base price (1.0x multiplier)
- **VIP**: Base price × 1.5
- **Premium**: Base price × 2.0

## Key OOP Concepts Demonstrated

1. **Encapsulation**: Private fields with public getters/setters
2. **Inheritance**: Extending JPanel and JFrame classes
3. **Polymorphism**: Interface implementations and method overriding
4. **Abstraction**: Separation of concerns with MVC pattern
5. **Composition**: Objects containing other objects
6. **Static Methods**: Utility methods for price calculation

## Academic Compliance

✅ **Three Core Modules Only**: Home, Form, Receipt  
✅ **Basic Swing Components**: No advanced libraries  
✅ **In-Memory Storage**: ArrayLists for data management  
✅ **No Database Integration**: Simplified data handling  
✅ **No External Dependencies**: Pure Java implementation  
✅ **Clean MVC Architecture**: Organized code structure  
✅ **Professional UI**: Modern and user-friendly interface  

## System Requirements

- **Java SE 8 or higher**
- **Operating System**: Windows, macOS, or Linux
- **Memory**: Minimum 512MB RAM
- **Display**: 800x600 minimum resolution

## Author

**BIT 4043 Student**  
Object-Oriented Programming Course  
Academic Year 2025

---

*This project demonstrates fundamental OOP principles and GUI development using Java Swing for educational purposes.*
