
import javax.swing.*;
import java.awt.*;

/**
 * Main Frame for Mini Ticket System
 * Academic project for BIT 4043 OOP course
 */
public class MainFrame extends JFrame {
    private CardLayout cardLayout;
    private JPanel contentPanel;
    private BookingController bookingController;

    // Panel references
    private HomePanel homePanel;
    private BookingFormPanel bookingFormPanel;
    private ReceiptPanel receiptPanel;

    // Card names
    public static final String HOME_CARD = "HOME";
    public static final String BOOKING_CARD = "BOOKING";
    public static final String RECEIPT_CARD = "RECEIPT";

    public MainFrame() {
        bookingController = new BookingController();
        initializeFrame();
        initializePanels();
        setupLayout();
    }

    private void initializeFrame() {
        setTitle("Mini Ticket System - BIT 4043 OOP Project");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);
        setResizable(true);

        // Set application icon (optional)
        try {
            setIconImage(Toolkit.getDefaultToolkit().createImage(""));
        } catch (Exception e) {
            // Ignore if no icon available
        }
    }

    private void initializePanels() {
        homePanel = new HomePanel(this, bookingController);
        bookingFormPanel = new BookingFormPanel(this, bookingController);
        receiptPanel = new ReceiptPanel(this, bookingController);
    }

    private void setupLayout() {
        cardLayout = new CardLayout();
        contentPanel = new JPanel(cardLayout);

        // Add panels to card layout
        contentPanel.add(homePanel, HOME_CARD);
        contentPanel.add(bookingFormPanel, BOOKING_CARD);
        contentPanel.add(receiptPanel, RECEIPT_CARD);

        // Add content panel to frame
        add(contentPanel, BorderLayout.CENTER);

        // Show home panel initially
        showCard(HOME_CARD);
    }

    /**
     * Switch to specified card
     */
    public void showCard(String cardName) {
        cardLayout.show(contentPanel, cardName);
    }

    /**
     * Show home panel
     */
    public void showHome() {
        homePanel.refreshEventList();
        showCard(HOME_CARD);
    }

    /**
     * Show booking form for specific event
     */
    public void showBookingForm(int eventId) {
        bookingFormPanel.setSelectedEvent(eventId);
        showCard(BOOKING_CARD);
    }

    /**
     * Show receipt panel with booking details
     */
    public void showReceipt(Booking booking) {
        receiptPanel.displayBooking(booking);
        showCard(RECEIPT_CARD);
    }

    /**
     * Get booking controller
     */
    public BookingController getBookingController() {
        return bookingController;
    }
}
