<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="0f8456e8-ada1-4166-a1a6-79cc2814e4e0" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 7
}]]></component>
  <component name="ProjectId" id="2xa8ZqYKQJuXSZLFE3ra4x3JTf7" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Shell Script.compile_and_run.sh.executor": "Run",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/Users/<USER>/Desktop/MiniTicketSystem"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0f8456e8-ada1-4166-a1a6-79cc2814e4e0" name="Changes" comment="" />
      <created>1748164881634</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748164881634</updated>
    </task>
    <servers />
  </component>
</project>