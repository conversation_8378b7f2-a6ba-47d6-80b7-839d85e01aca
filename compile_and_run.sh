#!/bin/bash

# Compile and <PERSON> Script for Mini Ticket System
# Academic project for BIT 4043 OOP course

echo "=========================================="
echo "Mini Ticket System - Compilation & Run"
echo "BIT 4043 Object-Oriented Programming"
echo "=========================================="

# Clean previous compilation
echo "Cleaning previous compilation..."
find . -name "*.class" -delete

# Compile all Java files
echo "Compiling Java files..."
javac *.java

# Check if compilation was successful
if [ $? -eq 0 ]; then
    echo "Compilation successful!"
    echo "Starting Mini Ticket System..."
    echo "=========================================="

    # Run the application
    java Main
else
    echo "Compilation failed! Please check for errors."
    exit 1
fi
