
import javax.swing.*;
import java.awt.*;

/**
 * Main class for Mini Ticket System
 * Academic project for BIT 4043 Object-Oriented Programming course
 *
 * This is a simplified ticket booking system that demonstrates:
 * - Basic Java Swing GUI components
 * - MVC architecture pattern
 * - Object-oriented programming concepts
 * - In-memory data storage using ArrayLists
 *
 * Features:
 * 1. Home Screen: Display system information and available events
 * 2. Booking Form: Ticket booking form with price calculation
 * 3. Receipt: Display booking confirmation and details
 *
 * <AUTHOR> 4043 Student
 * @version 1.0
 */
public class Main {

    /**
     * Main method to start the Mini Ticket System application
     * @param args command line arguments (not used)
     */
    public static void main(String[] args) {
        // Set system look and feel for better appearance
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            // If system look and feel is not available, use default
            System.out.println("Could not set system look and feel, using default.");
        }

        // Create and display the application
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                try {
                    // Create main frame
                    MainFrame mainFrame = new MainFrame();

                    // Make the application visible
                    mainFrame.setVisible(true);

                    System.out.println("Mini Ticket System started successfully!");
                    System.out.println("Academic project for BIT 4043 OOP course");

                } catch (Exception e) {
                    // Handle any startup errors
                    System.err.println("Error starting application: " + e.getMessage());
                    e.printStackTrace();

                    // Show error dialog to user
                    JOptionPane.showMessageDialog(null,
                        "Error starting Mini Ticket System:\n" + e.getMessage(),
                        "Startup Error",
                        JOptionPane.ERROR_MESSAGE);
                }
            }
        });
    }
}
