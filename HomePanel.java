
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;

/**
 * Home Panel for Mini Ticket System
 * Academic project for BIT 4043 OOP course
 * Displays system information and available events
 */
public class HomePanel extends JPanel {
    private MainFrame mainFrame;
    private BookingController bookingController;
    private JPanel eventsPanel;
    private JScrollPane scrollPane;

    public HomePanel(MainFrame mainFrame, BookingController bookingController) {
        this.mainFrame = mainFrame;
        this.bookingController = bookingController;
        initializePanel();
    }

    private void initializePanel() {
        setLayout(new BorderLayout());
        setBackground(Color.WHITE);

        // Create header panel
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);

        // Create events panel
        createEventsPanel();
        add(scrollPane, BorderLayout.CENTER);

        // Create footer panel
        JPanel footerPanel = createFooterPanel();
        add(footerPanel, BorderLayout.SOUTH);
    }

    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(70, 130, 180));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Title
        JLabel titleLabel = new JLabel("Mini Ticket Booking System", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 28));
        titleLabel.setForeground(Color.WHITE);

        // Subtitle
        JLabel subtitleLabel = new JLabel("BIT 4043 Object-Oriented Programming Project", JLabel.CENTER);
        subtitleLabel.setFont(new Font("Arial", Font.ITALIC, 14));
        subtitleLabel.setForeground(Color.LIGHT_GRAY);

        // Welcome message
        JLabel welcomeLabel = new JLabel("Welcome! Browse our exciting events and book your tickets.", JLabel.CENTER);
        welcomeLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        welcomeLabel.setForeground(Color.WHITE);

        // Combine labels
        JPanel titlePanel = new JPanel(new GridLayout(3, 1, 5, 5));
        titlePanel.setBackground(new Color(70, 130, 180));
        titlePanel.add(titleLabel);
        titlePanel.add(subtitleLabel);
        titlePanel.add(welcomeLabel);

        headerPanel.add(titlePanel, BorderLayout.CENTER);
        return headerPanel;
    }

    private void createEventsPanel() {
        eventsPanel = new JPanel();
        eventsPanel.setLayout(new BoxLayout(eventsPanel, BoxLayout.Y_AXIS));
        eventsPanel.setBackground(Color.WHITE);
        eventsPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Add title for events section
        JLabel eventsTitle = new JLabel("Available Events");
        eventsTitle.setFont(new Font("Arial", Font.BOLD, 20));
        eventsTitle.setAlignmentX(Component.CENTER_ALIGNMENT);
        eventsPanel.add(eventsTitle);
        eventsPanel.add(Box.createVerticalStrut(20));

        // Load events
        refreshEventList();

        scrollPane = new JScrollPane(eventsPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
    }

    public void refreshEventList() {
        // Remove existing event panels (keep title)
        Component[] components = eventsPanel.getComponents();
        for (int i = 2; i < components.length; i++) {
            eventsPanel.remove(components[i]);
        }

        ArrayList<Event> events = bookingController.getAllEvents();

        for (Event event : events) {
            JPanel eventPanel = createEventPanel(event);
            eventsPanel.add(eventPanel);
            eventsPanel.add(Box.createVerticalStrut(15));
        }

        eventsPanel.revalidate();
        eventsPanel.repaint();
    }

    private JPanel createEventPanel(Event event) {
        JPanel eventPanel = new JPanel(new BorderLayout());
        eventPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(200, 200, 200), 1),
            BorderFactory.createEmptyBorder(15, 15, 15, 15)
        ));
        eventPanel.setBackground(new Color(248, 248, 248));
        eventPanel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 120));

        // Event details panel
        JPanel detailsPanel = new JPanel(new GridLayout(4, 1, 5, 2));
        detailsPanel.setBackground(new Color(248, 248, 248));

        JLabel nameLabel = new JLabel(event.getName());
        nameLabel.setFont(new Font("Arial", Font.BOLD, 16));
        nameLabel.setForeground(new Color(50, 50, 50));

        JLabel dateLabel = new JLabel("Date: " + event.getDate());
        dateLabel.setFont(new Font("Arial", Font.PLAIN, 12));

        JLabel venueLabel = new JLabel("Venue: " + event.getVenue());
        venueLabel.setFont(new Font("Arial", Font.PLAIN, 12));

        JLabel priceLabel = new JLabel("Starting from: $" + event.getBasePrice());
        priceLabel.setFont(new Font("Arial", Font.BOLD, 12));
        priceLabel.setForeground(new Color(0, 120, 0));

        detailsPanel.add(nameLabel);
        detailsPanel.add(dateLabel);
        detailsPanel.add(venueLabel);
        detailsPanel.add(priceLabel);

        // Book button
        JButton bookButton = new JButton("Book Tickets");
        bookButton.setPreferredSize(new Dimension(120, 40));
        bookButton.setBackground(new Color(70, 130, 180));
        bookButton.setForeground(Color.WHITE);
        bookButton.setFont(new Font("Arial", Font.BOLD, 12));
        bookButton.setFocusPainted(false);
        bookButton.setOpaque(true);
        bookButton.setBorderPainted(false);
        bookButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                mainFrame.showBookingForm(event.getId());
            }
        });

        eventPanel.add(detailsPanel, BorderLayout.CENTER);
        eventPanel.add(bookButton, BorderLayout.EAST);

        return eventPanel;
    }

    private JPanel createFooterPanel() {
        JPanel footerPanel = new JPanel(new FlowLayout());
        footerPanel.setBackground(new Color(240, 240, 240));
        footerPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JLabel footerLabel = new JLabel("© 2025 Mini Ticket System - Academic Project");
        footerLabel.setFont(new Font("Arial", Font.ITALIC, 10));
        footerLabel.setForeground(Color.GRAY);

        footerPanel.add(footerLabel);
        return footerPanel;
    }
}
