
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

/**
 * Simple Booking Controller for Mini Ticket System
 * Academic project for BIT 4043 OOP course
 * Handles business logic and data storage in memory
 */
public class BookingController {
    private ArrayList<Event> events;
    private ArrayList<Booking> bookings;
    private int nextBookingId;

    public BookingController() {
        this.events = new ArrayList<>();
        this.bookings = new ArrayList<>();
        this.nextBookingId = 1;
        initializeSampleEvents();
    }

    /**
     * Initialize sample events for demonstration
     */
    private void initializeSampleEvents() {
        events.add(new Event(1, "Rock Concert 2025", "2025-02-15", "City Arena",
                           "Amazing rock concert with top artists", new BigDecimal("50.00"), 500));
        events.add(new Event(2, "Classical Music Evening", "2025-02-20", "Symphony Hall",
                           "Beautiful classical music performance", new BigDecimal("75.00"), 300));
        events.add(new Event(3, "Comedy Show", "2025-02-25", "Comedy Club",
                           "Hilarious stand-up comedy night", new BigDecimal("30.00"), 200));
        events.add(new Event(4, "Jazz Festival", "2025-03-01", "Jazz Lounge",
                           "Smooth jazz music festival", new BigDecimal("60.00"), 250));
        events.add(new Event(5, "Theater Play", "2025-03-05", "Grand Theater",
                           "Classic theater performance", new BigDecimal("40.00"), 400));
    }

    /**
     * Get all available events
     */
    public ArrayList<Event> getAllEvents() {
        return new ArrayList<>(events);
    }

    /**
     * Get event by ID
     */
    public Event getEventById(int eventId) {
        for (Event event : events) {
            if (event.getId() == eventId) {
                return event;
            }
        }
        return null;
    }

    /**
     * Create a new booking
     */
    public Booking createBooking(String customerName, String customerEmail, String customerPhone,
                               int eventId, String seatType, int quantity) {
        Event event = getEventById(eventId);
        if (event == null) {
            return null;
        }

        if (!event.hasAvailableSeats(quantity)) {
            return null;
        }

        // Create booking date
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String bookingDate = sdf.format(new Date());

        // Create booking
        Booking booking = new Booking(nextBookingId++, customerName, customerEmail, customerPhone,
                                    event, seatType, quantity, bookingDate);

        // Update available seats
        event.bookSeats(quantity);

        // Store booking
        bookings.add(booking);

        return booking;
    }

    /**
     * Get all bookings
     */
    public ArrayList<Booking> getAllBookings() {
        return new ArrayList<>(bookings);
    }

    /**
     * Calculate price for given parameters
     */
    public BigDecimal calculatePrice(int eventId, String seatType, int quantity) {
        Event event = getEventById(eventId);
        if (event == null) {
            return BigDecimal.ZERO;
        }
        return Booking.calculateTotalPrice(event.getBasePrice(), seatType, quantity);
    }

    /**
     * Validate seat type
     */
    public boolean isValidSeatType(String seatType) {
        return Booking.SEAT_TYPE_STANDARD.equals(seatType) ||
               Booking.SEAT_TYPE_VIP.equals(seatType) ||
               Booking.SEAT_TYPE_PREMIUM.equals(seatType);
    }

    /**
     * Get available seat types
     */
    public String[] getSeatTypes() {
        return new String[]{
            Booking.SEAT_TYPE_STANDARD,
            Booking.SEAT_TYPE_VIP,
            Booking.SEAT_TYPE_PREMIUM
        };
    }
}
