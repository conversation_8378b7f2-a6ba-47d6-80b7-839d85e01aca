
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * Receipt Panel for Mini Ticket System
 * Academic project for BIT 4043 OOP course
 * Displays booking confirmation and receipt details
 */
public class ReceiptPanel extends JPanel {
    private MainFrame mainFrame;
    private BookingController bookingController;
    private JTextArea receiptTextArea;
    private JButton homeButton;
    private JButton newBookingButton;

    public ReceiptPanel(MainFrame mainFrame, BookingController bookingController) {
        this.mainFrame = mainFrame;
        this.bookingController = bookingController;
        initializePanel();
    }

    private void initializePanel() {
        setLayout(new BorderLayout());
        setBackground(Color.WHITE);

        // Create header
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);

        // Create receipt display area
        JPanel receiptPanel = createReceiptPanel();
        add(receiptPanel, BorderLayout.CENTER);

        // Create button panel
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }

    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(34, 139, 34)); // Forest Green
        headerPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        JLabel titleLabel = new JLabel("Booking Confirmation", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setForeground(Color.WHITE);

        JLabel subtitleLabel = new JLabel("Your tickets have been successfully booked!", JLabel.CENTER);
        subtitleLabel.setFont(new Font("Arial", Font.ITALIC, 14));
        subtitleLabel.setForeground(Color.LIGHT_GRAY);

        JPanel titlePanel = new JPanel(new GridLayout(2, 1, 5, 5));
        titlePanel.setBackground(new Color(34, 139, 34));
        titlePanel.add(titleLabel);
        titlePanel.add(subtitleLabel);

        headerPanel.add(titlePanel, BorderLayout.CENTER);
        return headerPanel;
    }

    private JPanel createReceiptPanel() {
        JPanel receiptPanel = new JPanel(new BorderLayout());
        receiptPanel.setBackground(Color.WHITE);
        receiptPanel.setBorder(BorderFactory.createEmptyBorder(30, 50, 30, 50));

        // Receipt title
        JLabel receiptTitle = new JLabel("BOOKING RECEIPT", JLabel.CENTER);
        receiptTitle.setFont(new Font("Courier New", Font.BOLD, 18));
        receiptTitle.setBorder(BorderFactory.createEmptyBorder(0, 0, 20, 0));

        // Receipt text area
        receiptTextArea = new JTextArea();
        receiptTextArea.setFont(new Font("Courier New", Font.PLAIN, 12));
        receiptTextArea.setEditable(false);
        receiptTextArea.setBackground(new Color(248, 248, 248));
        receiptTextArea.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(200, 200, 200), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));

        JScrollPane scrollPane = new JScrollPane(receiptTextArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setPreferredSize(new Dimension(600, 300));

        receiptPanel.add(receiptTitle, BorderLayout.NORTH);
        receiptPanel.add(scrollPane, BorderLayout.CENTER);

        return receiptPanel;
    }

    private JPanel createButtonPanel() {
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setBackground(new Color(240, 240, 240));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        homeButton = new JButton("Back to Home");
        homeButton.setPreferredSize(new Dimension(130, 35));
        homeButton.setBackground(new Color(70, 130, 180));
        homeButton.setForeground(Color.WHITE);
        homeButton.setFont(new Font("Arial", Font.BOLD, 12));
        homeButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                mainFrame.showHome();
            }
        });

        newBookingButton = new JButton("Book Another Event");
        newBookingButton.setPreferredSize(new Dimension(150, 35));
        newBookingButton.setBackground(new Color(34, 139, 34));
        newBookingButton.setForeground(Color.WHITE);
        newBookingButton.setFont(new Font("Arial", Font.BOLD, 12));
        newBookingButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                mainFrame.showHome();
            }
        });

        buttonPanel.add(homeButton);
        buttonPanel.add(Box.createHorizontalStrut(20));
        buttonPanel.add(newBookingButton);

        return buttonPanel;
    }

    public void displayBooking(Booking booking) {
        if (booking == null) {
            receiptTextArea.setText("No booking information available.");
            return;
        }

        StringBuilder receipt = new StringBuilder();
        receipt.append("===============================================\n");
        receipt.append("           MINI TICKET SYSTEM\n");
        receipt.append("         BIT 4043 OOP PROJECT\n");
        receipt.append("===============================================\n\n");

        receipt.append("BOOKING DETAILS:\n");
        receipt.append("-----------------------------------------------\n");
        receipt.append(String.format("Booking ID:      #%d\n", booking.getId()));
        receipt.append(String.format("Booking Date:    %s\n", booking.getBookingDate()));
        receipt.append("\n");

        receipt.append("CUSTOMER INFORMATION:\n");
        receipt.append("-----------------------------------------------\n");
        receipt.append(String.format("Name:            %s\n", booking.getCustomerName()));
        receipt.append(String.format("Email:           %s\n", booking.getCustomerEmail()));
        if (booking.getCustomerPhone() != null && !booking.getCustomerPhone().trim().isEmpty()) {
            receipt.append(String.format("Phone:           %s\n", booking.getCustomerPhone()));
        }
        receipt.append("\n");

        receipt.append("EVENT INFORMATION:\n");
        receipt.append("-----------------------------------------------\n");
        receipt.append(String.format("Event:           %s\n", booking.getEvent().getName()));
        receipt.append(String.format("Date:            %s\n", booking.getEvent().getDate()));
        receipt.append(String.format("Venue:           %s\n", booking.getEvent().getVenue()));
        receipt.append(String.format("Description:     %s\n", booking.getEvent().getDescription()));
        receipt.append("\n");

        receipt.append("TICKET INFORMATION:\n");
        receipt.append("-----------------------------------------------\n");
        receipt.append(String.format("Seat Type:       %s\n", booking.getSeatType()));
        receipt.append(String.format("Quantity:        %d ticket(s)\n", booking.getQuantity()));
        receipt.append(String.format("Base Price:      $%.2f per ticket\n", booking.getEvent().getBasePrice()));
        receipt.append(String.format("Total Amount:    $%.2f\n", booking.getTotalPrice()));
        receipt.append("\n");

        receipt.append("===============================================\n");
        receipt.append("Thank you for using Mini Ticket System!\n");
        receipt.append("Please keep this receipt for your records.\n");
        receipt.append("===============================================\n");

        receiptTextArea.setText(receipt.toString());
        receiptTextArea.setCaretPosition(0); // Scroll to top
    }
}
